<template>
  <Transition name="chat-dialog-fade" appear @after-leave="handleAfterLeave">
    <div v-if="visible" class="chat-dialog-overlay" @click="handleOverlayClick">
      <div class="chat-dialog" :class="{ show: visible }" @click.stop>
        <!-- 对话头部 -->
        <div class="chat-header">
          <!-- 顶部标题与开关 -->
          <div class="header-bar">
            <h3 class="header-title">与老董聊聊</h3>
            <div class="header-actions">
              <span class="auto-read-label">自动朗读</span>
              <van-switch v-model="autoReadEnabled" :size="28" @change="handleAutoReadToggle" />
            </div>
          </div>

          <div class="header-left" @click="handleHeaderClick">
            <div class="header-indicator"></div>
            <div class="header-arrow">
              <div class="arrow-down"></div>
            </div>
          </div>
        </div>

        <!-- 对话消息区域 -->
        <div ref="chatMessagesRef" class="chat-messages">
          <template v-for="(message, index) in messages" :key="message.key">
            <ChatItem
              :message-data="message"
              :is-regenerate="index === messages.length - 1"
              @regenerate="handleRegenerate"
            />
          </template>

          <!-- 空状态 -->
          <div v-if="messages.length === 0" class="empty-state"></div>
        </div>

        <!-- 输入框区域 -->
        <div
          class="chat-input-area"
          :class="{
            'voice-active': isRecording,
          }"
        >
          <!-- 统一的输入模式 - 始终显示输入框 -->
          <div class="keyboard-wrapper">
            <div class="input-box">
              <div class="input-content-wrapper">
                <van-field
                  ref="inputRef"
                  v-model="inputMessage"
                  class="input-content"
                  rows="1"
                  autosize
                  autocomplete="off"
                  inputmode="text"
                  type="textarea"
                  :placeholder="isRecording ? '我在听，请说...' : '可以问我任何问题'"
                  @keydown.enter.prevent="handleSendMessage"
                  @compositionstart="handleComposition"
                  @compositionupdate="handleComposition"
                  @compositionend="handleComposition"
                  @input="handleInputChange"
                />
                <!-- 麦克风按钮在输入框内部右侧 -->
                <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
                  <MicrophoneIcon :size="16" />
                </div>
              </div>
            </div>

            <!-- 用户头像 -->
            <div class="user-avatar-container">
              <div class="user-avatar" :style="{ backgroundColor: userRealAvatar ? 'transparent' : userAvatarColor }">
                <img v-if="userRealAvatar" :src="userRealAvatar" alt="用户头像" />
                <span v-else>{{ userAvatarLetter }}</span>
              </div>
            </div>

            <!-- 发送按钮 -->
            <div
              class="send-btn"
              :class="{
                'not-input': inputMessage === '',
              }"
            >
              <SendIcon
                class="send-icon"
                :size="46"
                @click="handleSendMessage"
              />
            </div>
          </div>
        </div>

        <!-- 老董假装说话样式 - 直接放在输入框下方 -->
        <div class="laodong-fake-speaking">
          <div class="fake-speaking-container">
            <div class="laodong-avatar">
              <img :src="selectedAssistantAvatar" alt="老董头像" />
            </div>
            <div class="fake-speaking-content">
              <div class="fake-speaking-text">老董会根据您的问题给出专业建议</div>
              <div class="fake-speaking-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onBeforeUnmount, computed, onMounted } from 'vue';
import ChatItem from '@/pages/Chat/components/chatItem.vue';
import { showToast, Field as vanField } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';
import SendIcon from '@/assets/icons/SendIcon.vue';
import { debounce } from 'lodash-es';
import { useUserStore } from '@/stores/user';
import { getUserProfile } from '@/apis/relation';
import { getUserInfo } from '@/apis/common';
// 导入头像图片
import avatar1 from '@/assets/icon/laodong1.jpg';
import avatar2 from '@/assets/icon/laodong2.jpg';
import avatar3 from '@/assets/icon/laodong3.png';
import avatar4 from '@/assets/icon/laodong4.png';
import avatar5 from '@/assets/icon/laodong5.png';
import avatar6 from '@/assets/icon/laodong6.jpg';

// Props
interface IProps {
  visible: boolean;
  messages: IChatStreamContent[];
  conversationId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'send-message', message: string): void;
  (e: 'regenerate', messageData: IChatStreamContent): void;
  (e: 'new-chat'): void;
}>();

// Refs
const chatMessagesRef = ref<HTMLElement>();
const inputMessage = ref('');
const inputRef = ref(); // 输入框引用
const userStore = useUserStore();

// 用户头像相关
const userRealAvatar = ref<string>('');

const userAvatarColor = computed(() => {
  const userId = userStore.userInfo?.login || 'user';
  // 简单的颜色生成逻辑
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
  const index = userId.length % colors.length;
  return colors[index];
});

const userAvatarLetter = computed(() => {
  const userId = userStore.userInfo?.login || 'U';
  // 返回用户ID的第一个字符作为头像字母
  return userId.charAt(0).toUpperCase();
});

// 获取用户真实头像
const loadUserAvatar = async () => {
  try {
    let userId = userStore.userInfo?.login;

    // 如果 userStore 中没有用户信息，直接调用 getUserInfo API
    if (!userId) {
      console.log('ℹ️ [ChatDialog.vue] userStore中没有用户信息，直接获取用户信息');
      try {
        const userInfo = await getUserInfo();
        if (userInfo && userInfo.login) {
          userId = userInfo.login;
          userStore.userInfo = userInfo; // 设置到 userStore 中
          console.log('✅ [ChatDialog.vue] 用户信息获取成功:', userId);
        } else {
          console.warn('⚠️ [ChatDialog.vue] 获取的用户信息格式异常');
          return;
        }
      } catch (error) {
        console.warn('⚠️ [ChatDialog.vue] 获取用户信息失败:', error);
        return;
      }
    }

    const userProfileResponse = await getUserProfile({
      user_id: userId,
    });

    if (userProfileResponse.result === 'success' && userProfileResponse.person?.avatar) {
      userRealAvatar.value = userProfileResponse.person.avatar;
      console.log('✅ [ChatDialog.vue] 用户头像加载成功:', userRealAvatar.value);
    } else {
      console.log('ℹ️ [ChatDialog.vue] 用户没有设置头像，使用字母头像');
    }
  } catch (error) {
    console.warn('⚠️ [ChatDialog.vue] 获取用户头像失败:', error);
  }
};

// 自动朗读开关
const autoReadEnabled = ref(false);
const handleAutoReadToggle = (val: boolean) => {
  console.log('🔊 [ChatDialog] 自动朗读切换为:', val);
};

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
// 保存媒体流引用，用于释放麦克风资源
let mediaStream: MediaStream | null = null;

// 响应式数据
const micPermission = ref(false); // 麦克风权限
const sessionId = ref(''); // 语音转文字sessionId
const audioBufferIndex = ref(0); // 语音转文字流序列号
const lastBuffer = ref(); // 语音转文字最后一条流
const voiceMessage = ref(''); // 发送的对话文字
const isRecording = ref(false); // 是否录音输入

const isOnComposition = ref(false); // 是否在输入法组合状态
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新

// AI助理头像选择的存储键
const AI_ASSISTANT_STORAGE_KEY = 'selectedAssistantIndex';

// AI助手数据
const assistants = ref([
  {
    id: 1,
    name: '老董',
    avatar: avatar1,
  },
  {
    id: 2,
    name: '老董',
    avatar: avatar2,
  },
  {
    id: 3,
    name: '老董',
    avatar: avatar3,
  },
  {
    id: 4,
    name: '老董',
    avatar: avatar4,
  },
  {
    id: 5,
    name: '老董',
    avatar: avatar5,
  },
  {
    id: 6,
    name: '老董',
    avatar: avatar6,
  },
]);

// 立即从localStorage获取初始索引，避免跳变
const getInitialAssistantIndex = (): number => {
  const savedIndex = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  const index = savedIndex ? parseInt(savedIndex, 10) : 0;
  console.log('🔍 [ChatDialog.vue] 获取初始助手索引:', { savedIndex, index });
  if (index >= 0 && index < 6) {
    // 助手数组长度为6
    console.log('✅ [ChatDialog.vue] 使用保存的助手索引:', index);
    return index;
  }
  console.log('⚠️ [ChatDialog.vue] 使用默认助手索引: 0');
  return 0;
};

// 响应式的助手索引，立即初始化为正确值
const currentAssistantIndex = ref(getInitialAssistantIndex());

// 获取选中的助手头像
const selectedAssistantAvatar = computed(() => {
  const index = currentAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    console.log(
      '✅ [ChatDialog.vue] 计算属性返回助手头像:',
      assistants.value[index].name,
      assistants.value[index].avatar,
    );
    return assistants.value[index].avatar;
  }
  console.log(
    '⚠️ [ChatDialog.vue] 计算属性返回默认助手头像:',
    assistants.value[0].name,
    assistants.value[0].avatar,
  );
  return assistants.value[0].avatar;
});

// 处理覆盖层点击（点击上半部分收起）
const handleOverlayClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const dialogElement = target.closest('.chat-dialog');

  // 如果点击的不是对话框内部，则关闭
  if (!dialogElement) {
    console.log('🔽 [ChatDialog] 点击覆盖层，收起对话框');
    emit('close');
  }
};

// 处理头部点击（收起功能）
const handleHeaderClick = () => {
  // 清空输入框
  inputMessage.value = '';
  emit('close');
};

// 处理重新生成
const handleRegenerate = (messageData: IChatStreamContent) => {
  emit('regenerate', messageData);
};

// 处理输入变化
const handleInputChange = () => {
  // 可以在这里添加输入验证逻辑
};

// 处理输入法组合事件
const handleComposition = (e: CompositionEvent) => {
  const { type } = e;
  isOnComposition.value = type !== 'compositionend';
};

// 在光标位置插入文字的工具函数
const insertTextAtCursor = (newText: string) => {
  if (!inputRef.value) return;

  const inputElement =
    inputRef.value.$el.querySelector('textarea') || inputRef.value.$el.querySelector('input');
  if (!inputElement) return;

  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = inputMessage.value;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  inputMessage.value = newValue;

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 处理语音按钮点击 - 直接开始录音
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    console.log('✅ [ChatDialog] 麦克风权限获取成功');
  } catch (error) {
    console.error('❌ [ChatDialog] 麦克风权限获取失败:', error);
    micPermission.value = false;
    showToast('麦克风权限获取失败，请检查浏览器设置');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      try {
        const { data: responseData } = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: data.buffer,
        });

        // 检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
        if (
          responseData.full_text &&
          responseData.full_text.trim() !== '' &&
          responseData.full_text !== lastVoiceText.value
        ) {
          // 计算新增的文字部分
          const newText = responseData.full_text;
          const previousText = lastVoiceText.value;

          // 如果新文字包含之前的文字，只插入新增部分
          let textToInsert = newText;
          if (previousText && newText.startsWith(previousText)) {
            textToInsert = newText.slice(previousText.length);
          }

          // 在光标位置插入新文字
          if (textToInsert) {
            insertTextAtCursor(textToInsert);
          }

          lastVoiceText.value = newText;
          voiceMessage.value = newText;
          void autoSendTimeout();
        }
      } catch (error) {
        console.error('❌ [ChatDialog] 语音识别失败:', error);
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    console.log('📤 [ChatDialog] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    // 不需要再次设置 inputMessage.value，避免覆盖用户可能的编辑
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  // 释放麦克风资源
  releaseMicrophoneResources();
  // 清空语音消息
  voiceMessage.value = '';
  console.log('🚫 [ChatDialog] 录音已取消');
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 处理发送消息
const handleSendMessage = () => {
  const message = inputMessage.value.trim();
  if (!message) return;

  console.log('📤 [ChatDialog] 发送消息:', message);
  emit('send-message', message);

  // 清空输入框
  inputMessage.value = '';
};

// 处理过渡动画结束后的清理
const handleAfterLeave = () => {
  console.log('🧹 [ChatDialog] 对话框完全关闭，清理状态');
  // 确保输入框被清空
  inputMessage.value = '';
  // 确保body overflow样式被重置
  document.body.style.overflow = '';
  // 强制重新渲染，确保DOM完全清理
  void nextTick(() => {
    console.log('✅ [ChatDialog] DOM清理完成');
  });
};

// 滚动到底部
const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTo({
      top: chatMessagesRef.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    void nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true },
);

// 监听显示状态变化，显示时滚动到底部
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      void nextTick(() => {
        scrollToBottom();
      });
    } else {
      // 对话框关闭时清空输入框
      inputMessage.value = '';
      // 清空语音消息
      voiceMessage.value = '';
      // 如果正在录音，停止录音
      if (isRecording.value) {
        cancelRecording();
      }
      // 确保body overflow样式被重置
      document.body.style.overflow = '';
    }
  },
);

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    cancelRecording();
  }
  releaseMicrophoneResources();
});

// 组件挂载时加载用户头像
onMounted(() => {
  void loadUserAvatar();
});
</script>

<style lang="scss" scoped>
// 使用全局主题变量，不再定义本地变量

.chat-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
  pointer-events: auto;
}

.chat-dialog {
  width: 100%;
  max-width: 100vw;
  height: 70vh;
  background: var(--bg-glass-popup); // 使用弹窗专用背景
  border: 2px solid var(--border-accent);
  border-radius: 20px 20px 0 0;
  backdrop-filter: blur(20px); // 恢复毛玻璃效果
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out forwards;

  &.show {
    animation: slideUp 0.3s ease-out forwards;
  }

  &.hide {
    animation: slideDown 0.3s ease-in forwards;
  }
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-glass);
  display: flex;
  align-items: center;
  position: relative;
  min-height: 100px;

  // 顶部标题/开关栏（不影响点击收起区域）
  .header-bar {
    position: absolute;
    top: 12px;
    left: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    pointer-events: none; // 不拦截 header-left 的点击收起

    .header-title {
      margin: 0;
      padding: 0;
      // 与输入/内容主字体对齐（略小于先前 44px）
      font-size: calc(var(--font-size-2xl) + 6px);
      line-height: 1.2;
      font-weight: 700;
      color: var(--primary-color); // 纯色不透明
      letter-spacing: 0.5px;
      text-shadow: none;
      opacity: 1;
    }

    .header-actions {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      pointer-events: auto; // 允许开关交互

      .auto-read-label {
        font-size: 28px; // 保持放大
        color: var(--primary-color); // 纯色不透明
        text-shadow: none;
        opacity: 1;
      }
    }
  }

  .header-left {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 100%;
    position: relative;

    .header-indicator {
      width: 40px;
      height: 4px;
      background: var(--accent-color);
      border-radius: 2px;
      position: absolute;
      left: 50%;
      top: -8px;
      transform: translateX(-50%);
    }

    .header-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8px;

      .arrow-down {
        width: 0;
        height: 0;
        border-left: 14px solid transparent;
        border-right: 14px solid transparent;
        border-top: 18px solid var(--text-primary);
        transition: transform 0.2s ease;
      }
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 400;
  }
}

.chat-input-area {
  width: 100%;
  min-height: auto;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 60%,
    rgba(0, 0, 0, 0.3) 100%
  );
  border: 2px solid var(--border-accent);
  backdrop-filter: blur(20px);
  border-radius: 20px 20px 0px 0px;
  padding: 16px 23px;
  box-sizing: border-box;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 36px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 36px);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  transition: all 0.3s ease;
  position: relative;

  // iOS Safari 兼容性修复
  @supports (-webkit-touch-callout: none) {
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.1) 60%,
      rgba(0, 0, 0, 0.3) 100%
    ) !important;
    border-color: var(--border-accent) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  // 输入模式样式
  .keyboard-wrapper {
    display: flex;
    align-items: center; // 改为中心对齐
    width: 100%;

    .input-box {
      flex: 1;
      display: flex;
      align-items: stretch;
      margin-right: 12px; // 减少右边距，为更大的按钮留出空间

      .input-content-wrapper {
        width: 100%;
        position: relative;
        display: flex;
        align-items: stretch;

        .voice-toggle-inner {
          position: absolute;
          right: 20px; // 增加右边距
          top: 50%;
          transform: translateY(-50%);
          width: 100px; // 增大语音按钮，更容易点击
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 50%;
          background: var(--bg-glass);
          border: 3px solid var(--border-accent); // 统一边框粗细为3px
          backdrop-filter: blur(20px);
          transition: all 0.3s ease;
          z-index: 10;
          box-shadow: var(--shadow-medium); // 添加统一阴影

          &:hover {
            background: var(--bg-glass-hover);
            border-color: var(--accent-color);
            transform: translateY(-50%) scale(1.05);
          }

          &.breathing {
            animation: breathing 2s ease-in-out infinite;
          }

          .iconfont {
            font-size: 48px; // 增大语音按钮图标，更容易识别和点击
            color: var(--text-primary);
          }
        }
      }

      .input-content {
        width: 100%;
        max-height: 192px;
        min-height: 88px;
        padding: 24px 90px 24px 24px !important; // 右侧留出更多空间给更大的麦克风按钮
        box-sizing: border-box;
        border-radius: var(--border-radius-xl);
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        box-shadow: var(--shadow-medium);

        // iOS Safari 兼容性修复
        @supports (-webkit-touch-callout: none) {
          background: var(--bg-glass) !important;
          border-color: var(--border-accent) !important;
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        &:focus-within {
          border-color: var(--accent-color);
          box-shadow:
            0 0 0 3px var(--accent-color-light),
            var(--shadow-strong);
          transform: translateY(-2px);
        }

        :deep(.van-field__control) {
          color: var(--text-primary);
          font-size: calc(var(--font-size-2xl) + 6px) !important;
          line-height: 1.4;
          max-height: 165px;
          font-weight: 500;
          padding-right: 120px !important; // 为更大的语音按钮留出更多空间
        }

        :deep(.van-field__control::placeholder) {
          color: var(--placeholder-color);
          font-size: calc(var(--font-size-2xl) + 6px) !important;
          font-weight: 400;
        }
      }
    }

    .user-avatar-container {
      display: flex;
      align-items: center;
      margin-right: 12px;

      .user-avatar {
        width: 100px; // 增大用户头像，更容易点击
        height: 100px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 36px; // 增大字体大小
        font-weight: bold;
        border: 3px solid var(--border-glass); // 统一边框粗细为3px
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        box-shadow: var(--shadow-medium);
        overflow: hidden; // 确保图片不会溢出圆形边界

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--shadow-strong);
        }
      }
    }

    .send-btn {
      width: 70px;
      height: 70px;
      flex-shrink: 0;
      background: var(--primary-color-light);
      border: 2px solid var(--primary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      flex-shrink: 0;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-medium); // 统一阴影效果

      &.not-input {
        background: var(--bg-glass);
        border-color: var(--primary-color);
        .iconfont {
          color: var(--text-disabled);
        }
      }

      &.loading-input {
        background: var(--accent-color-light);
        border-color: var(--accent-color);
        .iconfont {
          font-size: 60px;
          color: var(--accent-color);
        }
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .send-icon {
      border: none;
      background: none;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
      max-width: 46px;
      max-height: 46px;
    }
  }

  // 语音输入模式样式
  .voice-text-display {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--bg-glass);
    border: 2px solid var(--border-accent);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-strong), var(--shadow-accent);
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    .voice-placeholder {
      color: var(--text-tertiary);
      font-size: calc(var(--font-size-xl) + 4px);
      font-weight: 400;
      text-align: center;
    }

    .voice-message-text {
      color: var(--text-primary);
      font-size: calc(var(--font-size-2xl) + 4px);
      font-weight: 500;
      line-height: 1.4;
      text-align: center;
    }
  }

  .button-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
  }
}

// Transition 动画
.chat-dialog-fade-enter-active,
.chat-dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}

.chat-dialog-fade-enter-from,
.chat-dialog-fade-leave-to {
  opacity: 0;
}

// 确保离开动画完成后元素完全移除
.chat-dialog-fade-leave-to {
  pointer-events: none;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes wave {
  0%,
  100% {
    height: 20px;
  }
  50% {
    height: 60px;
  }
}

@keyframes breathing {
  0% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--accent-color-strong);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 0 8px var(--accent-color-light);
  }
  100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--accent-color-strong);
  }
}

// 老董假装说话样式 - 作为输入框的下半部分
.laodong-fake-speaking {
  padding: 0px 20px 12px 20px; // 与输入框内部padding一致
  display: flex;
  justify-content: flex-start;
  // 使用与输入框相同的背景
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 60%,
    rgba(0, 0, 0, 0.3) 100%
  );
  border: 2px solid var(--border-accent); // 与输入框相同的边框
  border-top: none; // 去掉上边框，与输入框连接
  border-radius: 0 0 20px 20px; // 只有下方圆角，与输入框形成整体
  margin-top: -2px; // 与输入框无缝连接
  backdrop-filter: blur(20px); // 与输入框相同的毛玻璃效果

  .fake-speaking-container {
    display: flex;
    align-items: center; // 改为居中对齐，一行显示
    gap: 12px;
    width: 100%; // 占满宽度

    .laodong-avatar {
      width: 48px; // 适中的头像大小
      height: 48px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: #ffffff; // 纯色不透明，在深色背景下更醒目
        font-size: 20px; // 增大字体
        font-weight: 500;
        line-height: 1.2;
        white-space: nowrap; // 不换行
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: var(--accent-color);
          animation: fakeSpeakingPulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

// 假装说话动画
@keyframes fakeSpeakingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}
</style>
